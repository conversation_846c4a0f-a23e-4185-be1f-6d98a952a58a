# Chunk Distribution Command - Size-Based Threshold Examples

## Overview
The `chunkDistribution` command now supports version-based behavior:
- **MongoDB < 6.0**: Uses chunk count analysis
- **MongoDB ≥ 6.0**: Uses chunk size analysis with user-specified size thresholds

## Usage Examples

### MongoDB < 6.0 (Chunk Count Mode)
```bash
# Default: Critical if more than 1 undistributed chunk
node bin/index.js chunkDistribution -u "mongodb://localhost:27017"

# Custom threshold: Critical if more than 5 undistributed chunks
node bin/index.js chunkDistribution -u "mongodb://localhost:27017" -c 5
```

### MongoDB ≥ 6.0 (Chunk Size Mode)
```bash
# Default: Critical if chunk size difference > 10MB
node bin/index.js chunkDistribution -u "mongodb://localhost:27017"

# Custom thresholds with different size formats:
node bin/index.js chunkDistribution -u "mongodb://localhost:27017" -c "100MB"
node bin/index.js chunkDistribution -u "mongodb://localhost:27017" -c "1GB"
node bin/index.js chunkDistribution -u "mongodb://localhost:27017" -c "500KB"
node bin/index.js chunkDistribution -u "mongodb://localhost:27017" -c "50B"

# Exclude specific namespaces
node bin/index.js chunkDistribution -u "mongodb://localhost:27017" -c "100MB" -e "test.logs,analytics.temp"
```

## Supported Size Formats
- `B` - Bytes (e.g., `1024B`)
- `KB` - Kilobytes (e.g., `100KB`)
- `MB` - Megabytes (e.g., `10MB`)
- `GB` - Gigabytes (e.g., `1GB`)
- `TB` - Terabytes (e.g., `1TB`)

## Output Examples

### MongoDB < 6.0 Output
```
Ok[c: 1] - Chunks well balanced across shards / Version: x.x.x
```
or
```
Critical[c: 5] - 7 chunks are not well distributed across shards, Distribution Info: [ Namespace: "mydb.users", Message: 3 chunks from the shard 'shard01' were not properly distributed, Namespace: "mydb.orders", Message: 4 chunks from the shard 'shard02' were not properly distributed ] / Version: x.x.x
```

### MongoDB ≥ 6.0 Output
```
Ok[c: 10MB] - Chunk sizes well balanced across shards / Version: x.x.x
```
or
```
Critical[c: 100MB] - 157286400 bytes excess chunk sizes are not well distributed across shards, Distribution Info: [ Namespace: "mydb.users", Message: 157286400 bytes excess chunk size from the shard 'shard01' were not properly distributed ] / Version: x.x.x
```

## JSON Output
```json
{
  "type": "Checks",
  "exit_status": 2,
  "version": "x.x.x",
  "message": "Critical[c: 100MB] - 157286400 bytes excess...",
  "output": {
    "args": {
      "critical": "100MB",
      "criticalThreshold": 104857600,
      "type": "count",
      "mongoVersion": "6.0.5",
      "useChunkSize": true
    },
    "data": {
      "total_undistributed": 157286400,
      "distribution_info": [
        {
          "namespace": "mydb.users",
          "message": "157286400 bytes excess chunk size from the shard 'shard01' were not properly distributed",
          "sizeDifference": 157286400,
          "shard": "shard01"
        }
      ],
      "analysis_type": "chunk_size",
      "threshold_unit": "bytes"
    }
  }
}
```

## Key Changes
1. **Automatic Version Detection**: Detects MongoDB version and chooses appropriate analysis method
2. **Size Format Parsing**: Supports human-readable size formats (10MB, 1GB, etc.)
3. **Threshold Logic**: 
   - MongoDB < 6.0: Compares total undistributed chunks against threshold
   - MongoDB ≥ 6.0: Compares total excess size (in bytes) against size threshold
4. **Enhanced Output**: Shows analysis method and appropriate units in messages
5. **Backward Compatibility**: Still accepts plain numbers for MongoDB < 6.0
