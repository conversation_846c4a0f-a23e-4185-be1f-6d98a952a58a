#! /usr/bin/env node

const {
	Command
} = require('commander');
const program = new Command();
const mainfn = require('./initialize.js');
const packageJson = require('../package.json');

program
	.name('mydbmongoplugin')
	.description('MyDbops MongoDB Plugin - A command-line tool for managing and monitoring MongoDB instances')
	.version(packageJson.version, '-v, --version')

const defaultParam = {
	debug: false,
};

function argsBooleanValCheck() {
	defaultParam[this.long.split("--")[1]] = true;
	return true;
}

function argsValCheck(args) {
	if (args.charAt(0) === '-') {
		program.error("Syntax error: Missing argument");
	}
	defaultParam[this.long.split("--")[1]] = args;
	return args;
}

program
	.requiredOption('--mydbToken <token>', 'Specifies the authentication token for accessing the plugin. (Required)', argsValCheck)
	.requiredOption('--uri <mongodb-uri>', 'Specifies the MongoDB URI for database connection. (Required)', argsValCheck)
	.option('--tmpdir <dir>', 'Specifies the directory where temporary files are stored', argsValCheck)
	.option('--json', 'Print the output in JSON format', argsBooleanValCheck)
	.option('--debug', 'Enable debugging mode to generate detailed log messages', argsBooleanValCheck)
	.option('--legacy', 'Enforce the use of the legacy MongoDB driver', argsBooleanValCheck);

program
	.command('uptime')
	.description('Check the uptime of the MongoDB service since its last reboot')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 20 minutes)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 10 minutes)', argsValCheck)
	.option('--planCachePath <path>', 'Specify the path of the planCache backup to load in case of a Mongo restart', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "uptime";
	});

program
	.command('connect')
	.description('Check the time it takes to connect to the MongoDB server')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 2 seconds)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 4 seconds)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "connect";
	});

program
	.command('connections')
	.description('Check the total number of incoming connections from clients to the server')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 30 percent)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 40 percent)', argsValCheck)
	.option('-t, --type <value>', 'Specify the warning and critical threshold value type (default: "percent")', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "connections";
	});

program
	.command('memory')
	.description('Check the amount of resident memory currently used by the MongoDB server')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 75 percent)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 80 percent)', argsValCheck)
	.option('-t, --type <value>', 'Specify the warning and critical threshold value type (default: "percent")', argsValCheck)
	.option('--autoHeapReleaseEnable', 'Specify this option to release the free heap memory automatically (default: false)', argsBooleanValCheck)
	.option('--autoHeapReleaseRate <value>', 'Set the tcmallocReleaseRate value (default: 0)', argsValCheck)
	.option('--autoHeapReleaseInterval <value>', 'Specify the heap memory enable interval value in minutes (default: 15)', argsValCheck)
	.option('--heapReleaseThreshold <value>', 'Set a specific threshold for heap memory release (e.g., "60")', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "memory";
	});

program
	.command('createDatabases')
	.description('Verify if a new database has been created on the server')
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 1)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "createDatabases";
	});

program
	.command('dropDatabases')
	.description('Verify if any database has been dropped on the server')
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 1)', argsValCheck)
	.option('-e, --excludeDatabase <database>', 'Specify a database to exclude from the database lists (use "," delimiter for multiple databases)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "dropDatabases";
	});

program
	.command('createCollections')
	.description('Verify if new collections have been created in the database')
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 1)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "createCollections";
	});

program
	.command('dropCollections')
	.description('Verify if any collections have been dropped from the database')
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 1)', argsValCheck)
	.option('-e, --excludeNamespace <namespace>', 'Specify a namespace to exclude from the collection lists (use "," delimiter for multiple namespaces)', argsValCheck)
	.option('--excludeNamespaceRegex <patterns>', 'Comma-separated regex keywords to ignore matching namespaces', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "dropCollections";
	});

program
	.command('totalDBsUncompressedSize')
	.description('Check the size of the uncompressed data for the entire database')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 30 percent)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 40 percent)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "totalDBsUncompressedSize";
	});

program
	.command('databaseUnCompressedSize')
	.description('Check the size of the uncompressed data for each database')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 30 percent)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 40 percent)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "databaseUnCompressedSize";
	});

program
	.command('collectionUnCompressedSize')
	.description('Check the size of the uncompressed data for each collection')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 30 percent)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 40 percent)', argsValCheck)
	.option('-s, --size <value>', 'Verify collections whose size exceeds a specified value (default: "0bytes")', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "collectionUnCompressedSize";
	});

program
	.command('collectionDocuments')
	.description('Check the number of documents in each collection')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 30 percent)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 40 percent)', argsValCheck)
	.option('-s, --size <value>', 'Verify collections whose size exceeds a specified value (default: "0bytes")', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "collectionDocuments";
	});

program
	.command('databaseIndexesSize')
	.description('Ensure that the size of all indexes fits into the WiredTiger cache')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 70 percent)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 80 percent)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "databaseIndexesSize";
	});

program
	.command('queues')
	.description('Verify the number of operations currently queued and waiting for the read or write lock')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 5)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 10)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "queues";
	});

program
	.command('indexCount')
	.description('Check the number of indexes in each collection')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 55)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 60)', argsValCheck)
	.option('-e, --excludeNamespace <namespace>', 'Specify a namespace to exclude from the index count (use "," delimiter for multiple namespaces)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "indexCount";
	});

program
	.command('pageFaults')
	.description('Check the number of page faults that occur on the server')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 5 pages)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 10 pages)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "pageFaults";
	});

program
	.command('asserts')
	.description('Check the number of assertions raised on the server')
	.requiredOption('-a, --assertType <type>', 'Specify the assertion type', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 1)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "asserts";
	});

program
	.command('readTicket')
	.description('Verify the number of read tickets currently in use')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 25 percent)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 35 percent)', argsValCheck)
	.option('-t, --type <value>', 'Specify the warning and critical threshold value type (default: "percent")', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "readTicket";
	});

program
	.command('writeTicket')
	.description('Verify the number of write tickets currently in use')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 25 percent)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 35 percent)', argsValCheck)
	.option('-t, --type <value>', 'Specify the warning and critical threshold value type (default: "percent")', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "writeTicket";
	});

program
	.command('maxCacheSize')
	.description('Check the maximum wiredTiger cache size configured in the total system memory')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 75 percent)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 80 percent)', argsValCheck)
	.option('-t, --type <value>', 'Specify the warning and critical threshold value type (default: "percent")', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "maxCacheSize";
	});

program
	.command('currentCache')
	.description('Verify the amount of space (bytes) currently taken by cached data in the WiredTiger cache')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 82 percent)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 85 percent)', argsValCheck)
	.option('-t, --type <value>', 'Specify the warning and critical threshold value type (default: "percent")', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "currentCache";
	});

program
	.command('openCursors')
	.description('Verify the number of cursors currently opened by MongoDB for clients')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 10)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 15)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "openCursors";
	});

program
	.command('noCursorTimeout')
	.description('Verify the number of open cursors with timeout disabled')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 2)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 5)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "noCursorTimeout";
	});

program
	.command('timedOutCursors')
	.description('Verify the number of cursors that have timed out during the selected time period')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: 2)', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 5)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "timedOutCursors";
	});

program
	.command('dirtyCache')
	.description('Verify the amount of space taken by dirty data in the WiredTiger cache')
	.option('-w, --warning <value>', 'Set the warning threshold value (default: "75MB")', argsValCheck)
	.option('-c, --critical <value>', 'Set the critical threshold value (default: "100MB")', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "dirtyCache";
	});

program
	.command('collScanQuery')
	.description('Check if any collection scan (collscan) queries are running on the server')
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 1)', argsValCheck)
	.option('-l, --limit <value>', 'Specify the value to limit the collscan query output (default: 5)', argsValCheck)
	.option('-s, --slowMs <milliSeconds>', 'Specify the query running time in milliseconds (default: 1 ms)', argsValCheck)
	.option('-e, --excludeNamespace <namespace>', 'Specify a namespace to exclude from the query results (use "," delimiter for multiple namespaces)', argsValCheck)
	.option('-a, --appName <name>', 'Specify a name to exclude from the query results (use "," delimiter for multiple appNames)', argsValCheck)
	.option('--ignoreChangeStream', 'Specify this option to ignore change stream queries', argsBooleanValCheck)
	.action(() => {
		defaultParam.subCommand = "collScanQuery";
	});

program
	.command('slowQuery')
	.description('Check if any slow queries are running on the server')
	.option('-c, --critical <value>', 'Set the critical threshold value (default: 1)', argsValCheck)
	.option('-l, --limit <value>', 'Specify the value to limit the output (default: 5)', argsValCheck)
	.option('-s, --slowMs <milliSeconds>', 'Specify the query running time in milliseconds (default: 500 ms)', argsValCheck)
	.option('-e, --excludeNamespace <namespace>', 'Specify a namespace to exclude from the query results (use "," delimiter for multiple namespaces)', argsValCheck)
	.option('-a, --appName <name>', 'Specify a name to exclude from the query results (use "," delimiter for multiple appNames)', argsValCheck)
	.option('--ignoreChangeStream', 'Specify this option to ignore change stream queries', argsBooleanValCheck)
	.action(() => {
		defaultParam.subCommand = "slowQuery";
	});

program
	.command('fragmentation')
	.description('Verify the fragmentation size of collections')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 30 percent)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 40 percent)', argsValCheck)
	.option('-s, --size <value>', 'Specify the collections compressed size threshold to consider for fragmentation checks (default: "0bytes")', argsValCheck)
	.option('-l, --limit <value>', 'Specify the value to limit the data fragmentation output (default: 5)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "fragmentation";
	});

program
	.command('idxFragmentation')
	.description('Verify the fragmentation size of index')
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 30 percent)', argsValCheck)
	.option('-s, --size <value>', 'Specify the collections compressed size threshold to consider for fragmentation checks (default: "0bytes")', argsValCheck)
	.option('-l, --limit <value>', 'Specify the value to limit the data fragmentation output (default: 5)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "idxFragmentation";
	});

program
	.command('indexStatus')
	.description('Verify if there are any index creation operations running on the MongoDB server')
	.action(() => {
		defaultParam.subCommand = "indexStatus";
	});

program
	.command('flowControl')
	.description('Verify if flow control is engaged on the MongoDB server')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 1)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 2)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "flowControl";
	});

program
	.command('collScanCount')
	.description('Verify the number of queries that performed a collection scan')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 10)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 15)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "collScanCount";
	});

program
	.command('blkSortCount')
	.description('Verify the number of queries that performed block sorting')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 10)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 15)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "blkSortCount";
	});

program
	.command('writeConflictsCount')
	.description('Verify the total number of queries that encountered write conflicts')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 10)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 15)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "writeConflictsCount";
	});

program
	.command('ttlDeletedCount')
	.description('Verify the total number of documents deleted from collections with a TTL (Time-To-Live) index')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 50000)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 100000)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "ttlDeletedCount";
	});

program
	.command('inspection')
	.description('Analyze log messages to identify potential issues')
	.option('-e, --excludeCode <name>', 'Specify a code name to exclude from log inspection (comma-separated for multiple code names)', argsValCheck)
	.option('--excludeComponent <name>', 'Specify a component name to exclude from log inspection (comma-separated for multiple component names)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "inspection";
	});

program
	.command('standardTuning')
	.description('Verify and optimize Linux and MongoDB tuning parameters')
	.option('-c, --config <value>', 'Specify the threshold values in JSON format', argsValCheck)
	.option('-e, --excludeParameter <parameter>', 'Exclude specific parameters from standard tuning results (comma-separated for multiple parameters)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "standardTuning";
	});

program
	.command('planCache')
	.description('Validate the consistency of the planCache across the replica set')
	.option('-s, --skipMember <memberName>', 'Specify the names of replica members to skip during checks (comma-separated for multiple values)', argsValCheck)
	.option('--skipReplica', 'Skip planCache validation across the replica set', argsBooleanValCheck)
	.option('--planCachePath <path>', 'Specify the path to store the planCache backup', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "planCache";
	});

program
	.command('unusedIndex')
	.description('Validate the presence of unused indexes across the replica set')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 5)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 10)', argsValCheck)
	.option('-d, --days <days>', 'Specify the number of days to consider indexes as unused (default: 60 days)', argsValCheck)
	.option('-r, --retentionDays <days>', 'Specify the retention days to keep unused index files (default: 10 days)', argsValCheck)
	.option('-s, --skipMember <memberName>', 'Specify the names of replica members to skip during checks (comma-separated for multiple values)', argsValCheck)
	.option('--skipReplica', 'Skip unused indexes validation across the replica set', argsBooleanValCheck)
	.option('--unusedIndexPath <path>', 'Specify the path to store the unusedIndex data', argsValCheck)
	.option('-l, --limit <value>', 'Specify the value to limit the unused index output (default: 5)', argsValCheck, 5)
	.action(() => {
		defaultParam.subCommand = "unusedIndex";
	});

program
	.command('replicationLag')
	.description('Check the replication lag to determine how far a secondary node is behind the primary node')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 30 seconds)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 60 seconds)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "replicationLag";
	});

program
	.command('replsetQuorum')
	.description('Check if the primary node is available in the replica set, ensuring quorum')
	.action(() => {
		defaultParam.subCommand = "replsetQuorum";
	});

program
	.command('replsetState')
	.description('Verify the current member state of each node in the replica set')
	.option('-s, --skipMember <member>', 'Specify the names of replica members to skip during checks (comma-separated for multiple values)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "replsetState";
	});

program
	.command('replicaPrimary')
	.description('Check whether the current primary node in the replica set has changed or not')
	.option('-r, --replicaset <replicaSetname>', 'Specify the replica set name', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "replicaPrimary";
	});

program
	.command('connectPrimary')
	.description('Check the time it takes to connect from a secondary server to the primary MongoDB server')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 2 seconds)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 4 seconds)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "connectPrimary";
	});

program
	.command('replicationHeadroom')
	.description('Check the difference between the primary node oplog window and the replication lag of the secondary nodes')
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 2 hours)', argsValCheck)
	.option('-s, --skipMember <member>', 'Specify the names of replica members to skip during checks (comma-separated for multiple values)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "replicationHeadroom";
	});

program
	.command('oplogDuration')
	.description('Verify the current oplog duration on the MongoDB server')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 24 hours)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 20 hours)', argsValCheck)
	.option('-o, --oplogHours <hours>', 'Specify the oplog hours to estimate the oplog size (default: 24 hours)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "oplogDuration";
	});

program
	.command('connectivity')
	.description('Verify connectivity between all connected nodes')
	.option('-t, --timeoutMs <milliseconds>', 'Specify a timeout for connection attempts in milliseconds (default: 1000)', argsValCheck)
	.option('-e, --excludeMember <member>', 'Specify members to skip during connectivity checks (comma-separated for multiple members)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "connectivity";
	});

program
	.command('balancerStatus')
	.description('Check the balancer status and detect failed balancer rounds')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 1)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 2)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "balancerStatus";
	});

program
	.command('chunkDistribution')
	.description('Verify that the chunks are properly distributed in the shard')
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 1 for chunk count, 10MB for chunk size)', argsValCheck)
	.option('-e, --excludeNamespace <namespace>', 'Specify a namespace to exclude from chunk distribution results (comma-separated for multiple namespaces)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "chunkDistribution";
	});

program
	.command('jumboChunk')
	.description('Analyze if any jumbo chunk is present in the shard cluster')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 5)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 10)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "jumboChunk";
	});

program
	.command('shardMetadataAndIndexConsistency')
	.description('Analyze shard metadata and index consistency information')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 1)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 2)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "shardMetadataAndIndexConsistency";
	});

program
	.command('shardIndexConsistencyCount')
	.description('Check index consistency for sharded collections')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 1)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 2)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "shardIndexConsistencyCount";
	});

program
	.command('orphanDocuments')
	.description('Check the number of orphaned documents in the shard')
	.option('-w, --warning <value>', 'Specify the warning threshold value (default: 100000)', argsValCheck)
	.option('-c, --critical <value>', 'Specify the critical threshold value (default: 150000)', argsValCheck)
	.action(() => {
		defaultParam.subCommand = "orphanDocuments";
	});

try {
	program.parse();
	const options = Object.assign(program.opts(), defaultParam);
	if (options.debug) {
		console.debug(`${new Date()} I Enabling debug mode`);
	}
	// Global Variables
	global.version = program.version();

	// Exit Code
	global.stateOk = 0;
	global.stateWarn = 1;
	global.stateCrit = 2;
	global.stateUkn = 3;
	global.debugMode = options.debug;

	mainfn(options);
} catch (error) {
	console.error(`Unknown - ${error}${(global.version) ? ` / Version: ${global.version}` : ""}`);
	process.exit(global.stateUkn);
}