const {
	debug, printFn
} = require('../others/common.js');

// Function to parse size format (10B, 100KB, 10MB, 10GB) and convert to bytes
const parseSizeThreshold = (sizeStr) => {
	if (!sizeStr || typeof sizeStr !== 'string') {
		return null;
	}

	// Check if it's a plain number (for backward compatibility)
	if (/^\d+$/.test(sizeStr)) {
		return parseInt(sizeStr);
	}

	// Parse size format like 10MB, 100KB, etc.
	const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*([KMGT]?B)$/i);
	if (!match) {
		return null;
	}

	const size = parseFloat(match[1]);
	const unit = match[2].toUpperCase();

	const multipliers = {
		'B': 1,
		'KB': 1024,
		'MB': 1024 * 1024,
		'GB': 1024 * 1024 * 1024,
		'TB': 1024 * 1024 * 1024 * 1024
	};

	return Math.round(size * (multipliers[unit] || 1));
};

const mainfn = async(client, options) => {

	const adminDb = client.db('admin');
	debug('D', 'Checks which process we are connecting to in mongos or mongod');

	try {
		const {
			ok
		} = await adminDb.command({
			isdbgrid: 1.0
		});
		if (ok === 0) {
			const msg = `This check doesn't work on mongod process`;
			debug('I', msg);
			printFn(options, "Ok", "log", msg);
		}
	} catch (err) {
		const msg = `This check doesn't work on mongod process`;
		debug('I', msg);
		printFn(options, "Ok", "log", msg);
	}

	// Get MongoDB version to determine logic approach
	debug('D', 'Fetching server version information');
	const {
		version
	} = await adminDb.command({
		serverStatus: 1.0
	});

	const majorVersion = parseFloat(version.split('.')[0] + '.' + version.split('.')[1]);
	const useChunkSize = majorVersion >= 6.0;
	debug('I', `MongoDB version: ${version}, Major version: ${majorVersion}, Using chunk size logic: ${useChunkSize}`);

	// Handle threshold based on version and user input
	let criticalThreshold;
	if (useChunkSize) {
		// For MongoDB 6.0+, try to parse as size format, fallback to default 10MB
		if (options.critical) {
			criticalThreshold = parseSizeThreshold(options.critical);
			if (criticalThreshold === null) {
				// If parsing failed, show error and exit
				const msg = `Invalid size format for critical threshold: "${options.critical}". Use format like 10B, 100KB, 10MB, 10GB`;
				debug('E', msg);
				printFn(options, "Critical", "error", msg);
			}
		} else {
			criticalThreshold = parseSizeThreshold('10MB'); // Default 10MB
		}
		debug('I', `Using chunk size threshold: ${criticalThreshold} bytes (${options.critical || '10MB'})`);
	} else {
		// For MongoDB < 6.0, use as chunk count
		criticalThreshold = parseInt(options.critical) || 1;
		debug('I', `Using chunk count threshold: ${criticalThreshold} chunks`);
	}

	debug('D', 'Checks if the chunk evenly distributed across the shards');
	const configDB = client.db('config');
	const firstDoc = await configDB.collection('chunks').findOne({}, {
		projection: {
			ns: 1,
			uuid: 1
		}
	});

	let chunkDetails;
	if (useChunkSize) {
		debug('D', 'Using chunk size aggregation for MongoDB 6.0+');
		// For MongoDB 6.0+, aggregate by chunk size instead of count
		chunkDetails = await configDB.collection('chunks').aggregate([{
			"$group": {
				_id: {
					"id": (firstDoc.ns) ? "$ns" : "$uuid",
					shard: "$shard"
				},
				count: {
					"$sum": 1
				},
				totalSize: {
					"$sum": { "$ifNull": ["$estimatedSizeBytes", 0] }
				}
			}
		}], { allowDiskUse: true }).toArray();
	} else {
		debug('D', 'Using chunk count aggregation for MongoDB < 6.0');
		// For MongoDB < 6.0, use traditional chunk count
		chunkDetails = await configDB.collection('chunks').aggregate([{
			"$group": {
				_id: {
					"id": (firstDoc.ns) ? "$ns" : "$uuid",
					shard: "$shard"
				},
				count: {
					"$sum": 1
				}
			}
		}], { allowDiskUse: true }).toArray();
	}
	if (chunkDetails.length === 0) {
		const msg = `This shard has no sharded collection`;
		debug('I', msg);
		printFn(options, "Ok", "log", msg);
	}

	const collNames = await configDB.collection('collections').find({}, {
		projection: {
			_id: 1,
			uuid: 1
		}
	}).toArray();

	debug('D', `Sharded collection lists: ${JSON.stringify(collNames)}`);
	debug('D', `Sharded collection count: ${collNames.length}`);

	if (collNames.length === 0) {
		const msg = `This shard has no sharded collection`;
		debug('I', msg);
		printFn(options, "Ok", "log", msg);
	}

	const shardCount = await configDB.collection('shards').estimatedDocumentCount();
	if (shardCount === 0) {
		const msg = `Unable to count number of shard servers in the cluster`;
		debug('I', msg);
		printFn(options, "Critical", "log", msg);
	}

	const groupedData = {};

	chunkDetails.forEach((item) => {
		const {
			id, shard
		} = item._id;
		const count = item.count;
		const totalSize = item.totalSize || 0;

		if (!groupedData[id]) {
			groupedData[id] = {
				id,
				shards: [],
				totalCount: 0,
				totalSize: 0
			};
		}

		const shardData = {
			shard,
			count
		};

		if (useChunkSize) {
			shardData.totalSize = totalSize;
		}

		groupedData[id].shards.push(shardData);
		groupedData[id].totalCount = groupedData[id].totalCount + count;
		groupedData[id].totalSize = groupedData[id].totalSize + totalSize;
	});

	const chunkArray = Object.values(groupedData);

	debug('D', `Total chunks info: ${JSON.stringify(chunkArray)}`);
	debug('D', `Analysis method: ${useChunkSize ? 'chunk size based (MongoDB 6.0+)' : 'chunk count based (MongoDB < 6.0)'}`);

	let out = [];
	options.excludeNamespace = (options.excludeNamespace) ? options.excludeNamespace.split(',') : [];
	debug('D', `Excluded namespace: ${(options.excludeNamespace.length > 0) ? options.excludeNamespace : '[]'}`);
	let totalChunk = 0;
	const lists = [];

	chunkArray.forEach(function(chunk) {
		if (useChunkSize) {
			// For MongoDB 6.0+: Use chunk size for distribution analysis
			const totalChunkSize = chunk.totalSize;
			const expectedSizePerShard = Math.round(totalChunkSize / shardCount);

			chunk.shards.forEach(function(shard) {
				const actualSize = shard.totalSize || 0;
				const sizeDifference = actualSize - expectedSizePerShard;

				// Only consider it imbalanced if size difference exceeds the threshold
				if (sizeDifference > criticalThreshold) {
					let namespace;
					if (!firstDoc.ns && firstDoc.uuid) {
						const ns = collNames.find((item) => JSON.stringify(item.uuid) == JSON.stringify(chunk.id));
						namespace = ns._id;
					} else {
						namespace = firstDoc.ns;
					}

					if (!namespace) {
						namespace = "Unknown";
					}

					if (!options.excludeNamespace.includes(namespace)) {
						totalChunk += sizeDifference; // Accumulate total excess size
						const msg = `Namespace: "${namespace}", Message: ${sizeDifference} bytes excess chunk size from the shard '${shard.shard}' were not properly distributed`;
						debug('I', msg);
						out.push(msg);
						if (options.json) lists.push({
							namespace: namespace,
							message: `${sizeDifference} bytes excess chunk size from the shard '${shard.shard}' were not properly distributed`,
							sizeDifference: sizeDifference,
							shard: shard.shard
						});
					}
				}
			});
		} else {
			// For MongoDB < 6.0: Use traditional chunk count
			const totalChunkCount = chunk.totalCount;
			const chunkCount = Math.round(totalChunkCount / shardCount);

			chunk.shards.forEach(function(shard) {
				if (chunkCount < shard.count) {
					const chunkDifference = shard.count - chunkCount;
					let namespace;
					if (!firstDoc.ns && firstDoc.uuid) {
						const ns = collNames.find((item) => JSON.stringify(item.uuid) == JSON.stringify(chunk.id));
						namespace = ns._id;
					} else {
						namespace = firstDoc.ns;
					}

					if (!namespace) {
						namespace = "Unknown";
					}

					if (!options.excludeNamespace.includes(namespace)) {
						totalChunk += chunkDifference;
						const msg = `Namespace: "${namespace}", Message: ${chunkDifference} chunks from the shard '${shard.shard}' were not properly distributed`;
						debug('I', msg);
						out.push(msg);
						if (options.json) lists.push({
							namespace: namespace,
							message: `${chunkDifference} chunks from the shard '${shard.shard}' were not properly distributed`,
							chunkDifference: chunkDifference,
							shard: shard.shard
						});
					}
				}
			});
		}
	});

	const distributionType = useChunkSize ? 'chunk sizes' : 'chunks';
	const thresholdUnit = useChunkSize ? 'bytes' : 'chunks';
	const msg = (totalChunk > 0) ?
		`${totalChunk} ${thresholdUnit} excess ${distributionType} are not well distributed across shards, Distribution Info: [ ${out.join(', ')} ]` :
		`${distributionType.charAt(0).toUpperCase() + distributionType.slice(1)} well balanced across shards`;
	debug('I', msg);

	if (options.json) var jsonData = {
		args: {
			critical: options.critical,
			criticalThreshold: criticalThreshold,
			type: options.type,
			mongoVersion: version,
			useChunkSize: useChunkSize
		},
		data: {
			total_undistributed: totalChunk,
			distribution_info: lists,
			analysis_type: useChunkSize ? 'chunk_size' : 'chunk_count',
			threshold_unit: thresholdUnit
		}
	};

	// For chunk size method, compare total excess size against threshold
	// For chunk count method, compare total undistributed chunks against threshold
	const finalThreshold = useChunkSize ? criticalThreshold : criticalThreshold;

	// Create a custom state check since we need different comparison logic
	debug('D', 'Performing state check');
	const isCritical = totalChunk > finalThreshold;

	if (isCritical) {
		printFn(options, "Critical", "log", msg, jsonData);
	} else {
		printFn(options, "Ok", "log", msg, jsonData);
	}
};

module.exports = mainfn;