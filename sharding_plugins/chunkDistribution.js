const {
	debug, stateCheck, defaultVal, thresholdDifference, printFn
} = require('../others/common.js');

const mainfn = async(client, options) => {
	defaultVal(options);
	thresholdDifference(options);

	const adminDb = client.db('admin');
	debug('D', 'Checks which process we are connecting to in mongos or mongod');

	try {
		const {
			ok
		} = await adminDb.command({
			isdbgrid: 1.0
		});
		if (ok === 0) {
			const msg = `This check doesn't work on mongod process`;
			debug('I', msg);
			printFn(options, "Ok", "log", msg);
		}
	} catch (err) {
		const msg = `This check doesn't work on mongod process`;
		debug('I', msg);
		printFn(options, "Ok", "log", msg);
	}

	// Get MongoDB version to determine logic approach
	debug('D', 'Fetching server version information');
	const {
		version
	} = await adminDb.command({
		serverStatus: 1.0
	});

	const majorVersion = parseFloat(version.split('.')[0] + '.' + version.split('.')[1]);
	const useChunkSize = majorVersion >= 6.0;
	debug('I', `MongoDB version: ${version}, Major version: ${majorVersion}, Using chunk size logic: ${useChunkSize}`);

	debug('D', 'Checks if the chunk evenly distributed across the shards');
	const configDB = client.db('config');
	const firstDoc = await configDB.collection('chunks').findOne({}, {
		projection: {
			ns: 1,
			uuid: 1
		}
	});

	let chunkDetails;
	if (useChunkSize) {
		debug('D', 'Using chunk size aggregation for MongoDB 6.0+');
		// For MongoDB 6.0+, aggregate by chunk size instead of count
		chunkDetails = await configDB.collection('chunks').aggregate([{
			"$group": {
				_id: {
					"id": (firstDoc.ns) ? "$ns" : "$uuid",
					shard: "$shard"
				},
				count: {
					"$sum": 1
				},
				totalSize: {
					"$sum": { "$ifNull": ["$estimatedSizeBytes", 0] }
				}
			}
		}], { allowDiskUse: true }).toArray();
	} else {
		debug('D', 'Using chunk count aggregation for MongoDB < 6.0');
		// For MongoDB < 6.0, use traditional chunk count
		chunkDetails = await configDB.collection('chunks').aggregate([{
			"$group": {
				_id: {
					"id": (firstDoc.ns) ? "$ns" : "$uuid",
					shard: "$shard"
				},
				count: {
					"$sum": 1
				}
			}
		}], { allowDiskUse: true }).toArray();
	}
	if (chunkDetails.length === 0) {
		const msg = `This shard has no sharded collection`;
		debug('I', msg);
		printFn(options, "Ok", "log", msg);
	}

	const collNames = await configDB.collection('collections').find({}, {
		projection: {
			_id: 1,
			uuid: 1
		}
	}).toArray();

	debug('D', `Sharded collection lists: ${JSON.stringify(collNames)}`);
	debug('D', `Sharded collection count: ${collNames.length}`);

	if (collNames.length === 0) {
		const msg = `This shard has no sharded collection`;
		debug('I', msg);
		printFn(options, "Ok", "log", msg);
	}

	const shardCount = await configDB.collection('shards').estimatedDocumentCount();
	if (shardCount === 0) {
		const msg = `Unable to count number of shard servers in the cluster`;
		debug('I', msg);
		printFn(options, "Critical", "log", msg);
	}

	const groupedData = {};

	chunkDetails.forEach((item) => {
		const {
			id, shard
		} = item._id;
		const count = item.count;
		const totalSize = item.totalSize || 0;

		if (!groupedData[id]) {
			groupedData[id] = {
				id,
				shards: [],
				totalCount: 0,
				totalSize: 0
			};
		}

		const shardData = {
			shard,
			count
		};

		if (useChunkSize) {
			shardData.totalSize = totalSize;
		}

		groupedData[id].shards.push(shardData);
		groupedData[id].totalCount = groupedData[id].totalCount + count;
		groupedData[id].totalSize = groupedData[id].totalSize + totalSize;
	});

	const chunkArray = Object.values(groupedData);

	debug('D', `Total chunks info: ${JSON.stringify(chunkArray)}`);
	debug('D', `Analysis method: ${useChunkSize ? 'chunk size based (MongoDB 6.0+)' : 'chunk count based (MongoDB < 6.0)'}`);

	let out = [];
	options.excludeNamespace = (options.excludeNamespace) ? options.excludeNamespace.split(',') : [];
	debug('D', `Excluded namespace: ${(options.excludeNamespace.length > 0) ? options.excludeNamespace : '[]'}`);
	let totalChunk = 0;
	const lists = [];

	chunkArray.forEach(function(chunk) {
		if (useChunkSize) {
			// For MongoDB 6.0+: Use chunk size for distribution analysis
			const totalChunkSize = chunk.totalSize;
			const expectedSizePerShard = Math.round(totalChunkSize / shardCount);

			chunk.shards.forEach(function(shard) {
				const actualSize = shard.totalSize || 0;
				if (expectedSizePerShard < actualSize) {
					const sizeDifference = actualSize - expectedSizePerShard;
					let namespace;
					if (!firstDoc.ns && firstDoc.uuid) {
						const ns = collNames.find((item) => JSON.stringify(item.uuid) == JSON.stringify(chunk.id));
						namespace = ns._id;
					} else {
						namespace = firstDoc.ns;
					}

					if (!namespace) {
						namespace = "Unknown";
					}

					if (!options.excludeNamespace.includes(namespace)) {
						totalChunk += 1; // Count as 1 imbalanced collection
						const msg = `Namespace: "${namespace}", Message: ${sizeDifference} bytes excess chunk size from the shard '${shard.shard}' were not properly distributed`;
						debug('I', msg);
						out.push(msg);
						if (options.json) lists.push({
							namespace: namespace,
							message: `${sizeDifference} bytes excess chunk size from the shard '${shard.shard}' were not properly distributed`,
							sizeDifference: sizeDifference,
							shard: shard.shard
						});
					}
				}
			});
		} else {
			// For MongoDB < 6.0: Use traditional chunk count
			const totalChunkCount = chunk.totalCount;
			const chunkCount = Math.round(totalChunkCount / shardCount);

			chunk.shards.forEach(function(shard) {
				if (chunkCount < shard.count) {
					const chunkDifference = shard.count - chunkCount;
					let namespace;
					if (!firstDoc.ns && firstDoc.uuid) {
						const ns = collNames.find((item) => JSON.stringify(item.uuid) == JSON.stringify(chunk.id));
						namespace = ns._id;
					} else {
						namespace = firstDoc.ns;
					}

					if (!namespace) {
						namespace = "Unknown";
					}

					if (!options.excludeNamespace.includes(namespace)) {
						totalChunk += chunkDifference;
						const msg = `Namespace: "${namespace}", Message: ${chunkDifference} chunks from the shard '${shard.shard}' were not properly distributed`;
						debug('I', msg);
						out.push(msg);
						if (options.json) lists.push({
							namespace: namespace,
							message: `${chunkDifference} chunks from the shard '${shard.shard}' were not properly distributed`,
							chunkDifference: chunkDifference,
							shard: shard.shard
						});
					}
				}
			});
		}
	});

	const distributionType = useChunkSize ? 'chunk sizes' : 'chunks';
	const msg = (totalChunk > 0) ?
		`${totalChunk} ${distributionType} are not well distributed across shards, Distribution Info: [ ${out.join(', ')} ]` :
		`${distributionType.charAt(0).toUpperCase() + distributionType.slice(1)} well balanced across shards`;
	debug('I', msg);

	if (options.json) var jsonData = {
		args: {
			critical: options.critical,
			type: options.type,
			mongoVersion: version,
			useChunkSize: useChunkSize
		},
		data: {
			total_undistributed: totalChunk,
			distribution_info: lists,
			analysis_type: useChunkSize ? 'chunk_size' : 'chunk_count'
		}
	};

	stateCheck(options, totalChunk, msg, jsonData);
};

module.exports = mainfn;